// 主要JavaScript功能文件

// 全局变量
let isScrolling = false;
let currentSection = 'home';

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 应用初始化
function initializeApp() {
    initializeNavigation();
    initializeAnimations();
    initializeScrollEffects();
    initializeModuleCards();
    initializeDocCards();
    initializeResponsiveFeatures();
    initializePerformanceCharts();

    // 添加页面加载动画
    document.body.classList.add('fade-in');

    console.log('GD32F470VET6 系统展示页面已加载');
}

// 导航功能初始化
function initializeNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // 汉堡菜单切换
    if (hamburger) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }

    // 导航链接点击处理
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            
            // 更新活动状态
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            
            // 滚动到目标部分
            scrollToSection(targetId);
            
            // 关闭移动端菜单
            if (navMenu.classList.contains('active')) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    });

    // 滚动时更新导航状态
    window.addEventListener('scroll', updateNavigationOnScroll);
}

// 滚动到指定部分
function scrollToSection(sectionId) {
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        const offsetTop = targetSection.offsetTop - 70; // 考虑导航栏高度
        
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
        
        currentSection = sectionId;
    }
}

// 滚动时更新导航状态
function updateNavigationOnScroll() {
    if (isScrolling) return;
    
    const sections = document.querySelectorAll('.section');
    const navLinks = document.querySelectorAll('.nav-link');
    const scrollPosition = window.scrollY + 100;

    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');

        if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${sectionId}`) {
                    link.classList.add('active');
                    currentSection = sectionId;
                }
            });
        }
    });
}

// 动画效果初始化
function initializeAnimations() {
    // 观察器选项
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    // 创建观察器
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in-up');
                
                // 为子元素添加延迟动画
                const children = entry.target.querySelectorAll('.overview-card, .module-card, .doc-card, .feature');
                children.forEach((child, index) => {
                    setTimeout(() => {
                        child.classList.add('fade-in-up');
                    }, index * 100);
                });
            }
        });
    }, observerOptions);

    // 观察需要动画的元素
    const animatedElements = document.querySelectorAll('.section, .overview-card, .module-card, .doc-card');
    animatedElements.forEach(el => observer.observe(el));
}

// 滚动效果初始化
function initializeScrollEffects() {
    let ticking = false;

    function updateScrollEffects() {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.hero-visual');
        
        parallaxElements.forEach(element => {
            const speed = 0.5;
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });

        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollEffects);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick);
}

// 模块卡片交互初始化
function initializeModuleCards() {
    const moduleCards = document.querySelectorAll('.module-card');
    
    moduleCards.forEach(card => {
        card.addEventListener('click', function() {
            const moduleType = this.getAttribute('data-module');
            showModuleDetails(moduleType);
        });

        // 添加悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 4px 6px -1px rgb(0 0 0 / 0.1)';
        });
    });
}

// 显示模块详细信息
function showModuleDetails(moduleType) {
    const moduleInfo = {
        scheduler: {
            title: '任务调度器模块',
            description: '时间片轮询调度算法，协调6个功能任务的执行',
            features: [
                '5ms最小调度周期',
                '预计算优化算法',
                '性能统计功能',
                '任务优先级管理'
            ],
            code: `
void scheduler_run(void) {
    uint32_t current_time = get_system_ms();
    
    for (uint8_t i = 0; i < task_num; i++) {
        if (current_time >= scheduler_task[i].next_run) {
            scheduler_task[i].task_func();
            scheduler_task[i].last_run = current_time;
            scheduler_task[i].next_run = current_time + scheduler_task[i].rate_ms;
        }
    }
}`
        },
        adc: {
            title: 'ADC采样模块',
            description: '12位高精度电压采样，支持可配置采样周期和变比系数',
            features: [
                '12位ADC分辨率',
                '0.8mV精度',
                'DMA传输优化',
                '超限检测功能'
            ],
            code: `
static inline float calculate_voltage(uint16_t adc_raw) {
    if (adc_raw > ADC_MAX_VALUE) {
        return 0.0f;
    }
    
    float voltage_raw = (adc_raw * ADC_REFERENCE_VOLTAGE) / ADC_MAX_VALUE;
    return voltage_raw * system_config.ratio_ch0;
}`
        },
        storage: {
            title: '存储管理模块',
            description: 'SD卡主存储+Flash备份的双重存储机制',
            features: [
                'FAT32文件系统',
                '数据加密支持',
                '自动备份机制',
                '文件完整性检查'
            ],
            code: `
void store_sample_data(float voltage, uint8_t over_limit) {
    char data_line[128];
    local_time_t current_time = get_current_local_time();
    
    snprintf(data_line, sizeof(data_line), 
        "%04d-%02d-%02d %02d:%02d:%02d ch0=%.2fV%s\\r\\n",
        current_time.year, current_time.month, current_time.day,
        current_time.hour, current_time.minute, current_time.second,
        voltage, over_limit ? " OverLimit!" : "");
        
    f_puts(data_line, &current_sample_file);
}`
        }
        // 可以继续添加其他模块的信息
    };

    const info = moduleInfo[moduleType];
    if (info) {
        showModal(info);
    }
}

// 文档卡片交互初始化
function initializeDocCards() {
    const docCards = document.querySelectorAll('.doc-card');
    
    docCards.forEach(card => {
        card.addEventListener('click', function() {
            const docType = this.getAttribute('data-doc');
            showDocumentContent(docType);
        });
    });
}

// 显示文档内容
function showDocumentContent(docType) {
    const docInfo = {
        manual: {
            title: '产品使用手册',
            content: '详细的产品介绍、硬件连接指南、软件配置说明和操作指导...',
            sections: ['产品概述', '硬件连接指南', '软件配置', '操作指南', '维护指导', '安全注意事项']
        },
        analysis: {
            title: '工程任务分析',
            content: '项目背景分析、技术选型依据、功能需求分析和开发环境配置...',
            sections: ['项目概述', '技术选型分析', '功能需求分析', '开发环境配置', '项目里程碑']
        },
        optimization: {
            title: '工程系统优化',
            content: '性能优化策略、内存优化方案、功耗优化技术和实时性优化...',
            sections: ['性能优化策略', '内存优化策略', '实时性优化', '代码优化策略', '资源利用分析']
        }
        // 可以继续添加其他文档的信息
    };

    const info = docInfo[docType];
    if (info) {
        showDocumentModal(info);
    }
}

// 显示模态框
function showModal(info) {
    // 创建模态框HTML
    const modalHTML = `
        <div class="modal-overlay" onclick="closeModal()">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h2>${info.title}</h2>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <p>${info.description}</p>
                    <h3>主要特性</h3>
                    <ul>
                        ${info.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                    ${info.code ? `
                        <h3>代码示例</h3>
                        <pre><code class="language-c">${info.code}</code></pre>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // 添加样式
    addModalStyles();
    
    // 代码高亮
    if (typeof Prism !== 'undefined') {
        Prism.highlightAll();
    }
}

// 显示文档模态框
function showDocumentModal(info) {
    const modalHTML = `
        <div class="modal-overlay" onclick="closeModal()">
            <div class="modal-content document-modal" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h2>${info.title}</h2>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <p>${info.content}</p>
                    <h3>文档章节</h3>
                    <div class="doc-sections">
                        ${info.sections.map(section => `
                            <div class="doc-section-item">
                                <i class="fas fa-file-text"></i>
                                <span>${section}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    addModalStyles();
}

// 关闭模态框
function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.remove();
    }
}

// 添加模态框样式
function addModalStyles() {
    if (!document.getElementById('modal-styles')) {
        const styles = `
            <style id="modal-styles">
                .modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                    animation: fadeIn 0.3s ease;
                }
                
                .modal-content {
                    background: white;
                    border-radius: 12px;
                    max-width: 800px;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    animation: slideInUp 0.3s ease;
                }
                
                .modal-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 1.5rem;
                    border-bottom: 1px solid #e2e8f0;
                }
                
                .modal-header h2 {
                    margin: 0;
                    color: var(--text-primary);
                }
                
                .modal-close {
                    background: none;
                    border: none;
                    font-size: 1.5rem;
                    cursor: pointer;
                    color: var(--text-secondary);
                    padding: 0.5rem;
                    border-radius: 50%;
                    transition: all 0.3s ease;
                }
                
                .modal-close:hover {
                    background: var(--bg-secondary);
                    color: var(--text-primary);
                }
                
                .modal-body {
                    padding: 1.5rem;
                }
                
                .modal-body h3 {
                    color: var(--primary-color);
                    margin: 1.5rem 0 1rem 0;
                }
                
                .modal-body ul {
                    list-style: none;
                    padding: 0;
                }
                
                .modal-body li {
                    padding: 0.5rem 0;
                    position: relative;
                    padding-left: 1.5rem;
                }
                
                .modal-body li::before {
                    content: '✓';
                    position: absolute;
                    left: 0;
                    color: var(--success-color);
                    font-weight: bold;
                }
                
                .modal-body pre {
                    background: #1e293b;
                    border-radius: 8px;
                    padding: 1rem;
                    overflow-x: auto;
                    margin: 1rem 0;
                }
                
                .modal-body code {
                    color: #e2e8f0;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                }
                
                .doc-sections {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1rem;
                    margin-top: 1rem;
                }
                
                .doc-section-item {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.75rem;
                    background: var(--bg-secondary);
                    border-radius: 8px;
                    border: 1px solid var(--border-color);
                    transition: all 0.3s ease;
                }
                
                .doc-section-item:hover {
                    background: var(--primary-color);
                    color: white;
                    transform: translateY(-2px);
                }
                
                .doc-section-item i {
                    color: var(--primary-color);
                }
                
                .doc-section-item:hover i {
                    color: white;
                }
                
                @media (max-width: 768px) {
                    .modal-content {
                        margin: 1rem;
                        max-width: calc(100% - 2rem);
                    }
                    
                    .doc-sections {
                        grid-template-columns: 1fr;
                    }
                }
            </style>
        `;
        document.head.insertAdjacentHTML('beforeend', styles);
    }
}

// 响应式功能初始化
function initializeResponsiveFeatures() {
    // 检测设备类型
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        // 移动端优化
        document.body.classList.add('mobile-device');
        
        // 禁用某些动画以提高性能
        const heavyAnimations = document.querySelectorAll('.float, .pulse, .breathing');
        heavyAnimations.forEach(el => {
            el.style.animation = 'none';
        });
    }

    // 窗口大小改变时的处理
    window.addEventListener('resize', debounce(function() {
        const newIsMobile = window.innerWidth <= 768;
        
        if (newIsMobile !== isMobile) {
            location.reload(); // 简单的响应式处理
        }
    }, 250));
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：平滑滚动到元素
function smoothScrollTo(element, duration = 1000) {
    const targetPosition = element.offsetTop - 70;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;

    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
    }

    function ease(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }

    requestAnimationFrame(animation);
}

// 性能图表初始化
function initializePerformanceCharts() {
    // 等待Chart.js加载完成
    if (typeof Chart === 'undefined') {
        setTimeout(initializePerformanceCharts, 100);
        return;
    }

    // 初始化资源使用率图表
    initResourceChart();

    // 初始化任务调度图表
    initTaskChart();

    // 初始化响应时间图表
    initResponseChart();
}

// 资源使用率图表
function initResourceChart() {
    const ctx = document.getElementById('resourceChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Flash已用', 'Flash可用', 'RAM已用', 'RAM可用'],
            datasets: [{
                data: [22.89, 489.11, 3.00, 189.00],
                backgroundColor: [
                    '#ef4444',
                    '#f1f5f9',
                    '#3b82f6',
                    '#e2e8f0'
                ],
                borderWidth: 2,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 15,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label;
                            const value = context.parsed;
                            if (label.includes('Flash')) {
                                return `${label}: ${value}KB`;
                            } else {
                                return `${label}: ${value}KB`;
                            }
                        }
                    }
                }
            }
        }
    });
}

// 任务调度图表
function initTaskChart() {
    const ctx = document.getElementById('taskChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['ADC任务', 'LED任务', 'OLED任务', '按键任务', '串口任务', 'RTC任务'],
            datasets: [{
                label: '调度周期 (ms)',
                data: [100, 50, 100, 5, 5, 500],
                backgroundColor: [
                    '#3b82f6',
                    '#10b981',
                    '#f59e0b',
                    '#ef4444',
                    '#8b5cf6',
                    '#06b6d4'
                ],
                borderRadius: 4,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed.y}ms`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '周期 (ms)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '任务模块'
                    }
                }
            }
        }
    });
}

// 响应时间图表
function initResponseChart() {
    const ctx = document.getElementById('responseChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'radar',
        data: {
            labels: ['按键响应', 'ADC延迟', '串口响应', '显示更新', '存储写入', '系统启动'],
            datasets: [{
                label: '响应时间 (ms)',
                data: [5, 0.5, 5, 100, 50, 200],
                backgroundColor: 'rgba(37, 99, 235, 0.2)',
                borderColor: '#2563eb',
                borderWidth: 2,
                pointBackgroundColor: '#2563eb',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.label}: ${context.parsed.r}ms`;
                        }
                    }
                }
            },
            scales: {
                r: {
                    beginAtZero: true,
                    max: 250,
                    ticks: {
                        stepSize: 50
                    }
                }
            }
        }
    });
}

// 导出全局函数
window.scrollToSection = scrollToSection;
window.closeModal = closeModal;